<?php
// 数据库配置文件
// 会员管理系统 - 深红色主题版本

// 数据库连接配置
$db_host = 'localhost';
$db_username = 'root';
$db_password = '111111';
$db_name = 'member_system2702';

// 数据库连接函数
function connectDB() {
    global $db_host, $db_username, $db_password, $db_name;
    
    try {
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", 
                       $db_username, $db_password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch(PDOException $e) {
        die("数据库连接失败: " . $e->getMessage());
    }
}

// 兼容旧版MySQL函数的连接
function mysql_connect_compat() {
    global $db_host, $db_username, $db_password, $db_name;
    
    try {
        $mysqli = new mysqli($db_host, $db_username, $db_password, $db_name);
        if ($mysqli->connect_error) {
            die('连接失败: ' . $mysqli->connect_error);
        }
        $mysqli->set_charset("utf8mb4");
        return $mysqli;
    } catch(Exception $e) {
        die("数据库连接失败: " . $e->getMessage());
    }
}

// 安全函数：防止SQL注入
function safe_query($pdo, $sql, $params = []) {
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch(PDOException $e) {
        error_log("SQL错误: " . $e->getMessage());
        return false;
    }
}

// HTML转义函数
function safe_output($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// 检查用户权限
function check_permission($required_level = 1) {
    session_start();
    if (!isset($_SESSION['username']) || !isset($_SESSION['ok'])) {
        header('Location: index.php');
        exit();
    }
    
    if ($_SESSION['ok'] < $required_level) {
        die('权限不足！');
    }
    
    return true;
}

// 系统配置
define('SYSTEM_NAME', '会员管理系统');
define('VERSION', '2.0');
define('THEME_COLOR', '#8B0000'); // 深红色主题

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');
?>