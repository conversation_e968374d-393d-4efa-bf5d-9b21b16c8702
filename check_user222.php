<?php
// 检查用户222的状态和权限
session_start();
require_once 'config.php';

echo "<h1>🔍 用户222权限检查</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .info { background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .success { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; color: #2e7d32; }
    .error { background: #ffebee; padding: 15px; border-radius: 8px; margin: 10px 0; color: #c62828; }
    .warning { background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; color: #856404; }
    .btn { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }
    .btn:hover { background: #005a8b; }
    .btn-success { background: #28a745; }
    .btn-danger { background: #dc3545; }
</style>";

// 检查登录状态
if (!isset($_SESSION['username'])) {
    echo "<div class='error'>❌ 未登录！请先登录系统。</div>";
    echo "<a href='index.php' class='btn'>🔑 前往登录</a>";
    exit();
}

echo "<div class='info'>";
echo "<h2>📋 当前登录信息</h2>";
echo "<strong>用户名：</strong>" . htmlspecialchars($_SESSION['username']) . "<br>";
echo "<strong>权限级别：</strong>" . ($_SESSION['ok'] == 1 ? '👑 管理员' : '🎯 普通用户') . " (ok=" . $_SESSION['ok'] . ")<br>";
echo "<strong>Session ID：</strong>" . session_id() . "<br>";
echo "</div>";

try {
    $pdo = connectDB();
    
    // 检查用户222是否存在
    $user222_stmt = $pdo->prepare("SELECT * FROM tab WHERE username = ?");
    $user222_stmt->execute(['222']);
    $user222 = $user222_stmt->fetch();
    
    echo "<h2>👤 用户222状态检查</h2>";
    
    if ($user222) {
        echo "<div class='success'>";
        echo "<strong>✅ 用户222存在</strong><br>";
        echo "ID: " . $user222['id'] . "<br>";
        echo "用户名: " . htmlspecialchars($user222['username']) . "<br>";
        echo "权限: " . ($user222['ok'] == 1 ? '👑 管理员' : '🎯 普通用户') . "<br>";
        echo "注册时间: " . $user222['regtime'] . "<br>";
        echo "</div>";
        
        // 检查用户222的内容
        echo "<h3>📄 用户222的内容</h3>";
        
        // 个人内容
        $personal_stmt = $pdo->prepare("SELECT * FROM content WHERE username = ? AND type = 'personal' ORDER BY updated_time DESC LIMIT 1");
        $personal_stmt->execute(['222']);
        $personal_content = $personal_stmt->fetch();
        
        echo "<div class='info'>";
        echo "<strong>🎯 个人专属内容：</strong>";
        if ($personal_content) {
            echo "存在<br>";
            echo "内容长度: " . mb_strlen($personal_content['content']) . " 字符<br>";
            echo "最后更新: " . $personal_content['updated_time'] . "<br>";
            echo "<details><summary>内容预览</summary><div style='background:#f8f9fa;padding:10px;margin:10px 0;border-radius:5px;max-height:200px;overflow:auto;'>";
            echo htmlspecialchars(mb_substr($personal_content['content'], 0, 500));
            if (mb_strlen($personal_content['content']) > 500) echo "...";
            echo "</div></details>";
        } else {
            echo "暂无<br>";
        }
        echo "</div>";
        
        // 权限检查
        echo "<h3>🔐 访问权限检查</h3>";
        
        if ($_SESSION['ok'] == 1) {
            echo "<div class='success'>";
            echo "<strong>🎉 您是管理员，有权限访问用户222的内容！</strong><br>";
            echo "根据 check.php 的权限逻辑，管理员可以查看任何用户的内容。<br>";
            echo "</div>";
            
            echo "<div class='info'>";
            echo "<strong>📋 权限逻辑说明：</strong><br>";
            echo "• check.php 第6行：check_permission(2) - 允许普通用户和管理员访问<br>";
            echo "• check.php 第13-16行：if (\$_SESSION['ok'] != 1 && \$target_username !== \$_SESSION['username']) - 只有非管理员才会被重定向<br>";
            echo "• 您的权限级别：ok=" . $_SESSION['ok'] . " (管理员)<br>";
            echo "• 结论：✅ 可以直接访问 check.php?username=222<br>";
            echo "</div>";
            
        } else {
            echo "<div class='warning'>";
            echo "<strong>⚠️ 您是普通用户，无法查看其他用户的内容</strong><br>";
            if ($_SESSION['username'] === '222') {
                echo "但您就是用户222，可以查看自己的内容。<br>";
            } else {
                echo "访问 check.php?username=222 会自动重定向到您自己的内容。<br>";
            }
            echo "</div>";
        }
        
    } else {
        echo "<div class='error'>";
        echo "<strong>❌ 用户222不存在</strong><br>";
        echo "需要先创建这个用户才能查看其内容。<br>";
        echo "</div>";
        
        // 提供创建用户的选项
        if ($_SESSION['ok'] == 1) {
            echo "<h3>➕ 创建用户222</h3>";
            
            if ($_POST && isset($_POST['create_user222'])) {
                try {
                    $password = password_hash('123456', PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("INSERT INTO tab (username, password, ok) VALUES (?, ?, 2)");
                    $stmt->execute(['222', $password]);
                    echo "<div class='success'>✅ 用户222创建成功！密码：123456</div>";
                    echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
                } catch (Exception $e) {
                    echo "<div class='error'>❌ 创建失败：" . $e->getMessage() . "</div>";
                }
            } else {
                echo "<form method='POST'>";
                echo "<p>作为管理员，您可以创建用户222：</p>";
                echo "<button type='submit' name='create_user222' class='btn btn-success'>创建用户222（密码：123456）</button>";
                echo "</form>";
            }
        }
    }
    
    // 测试链接
    echo "<h2>🔗 测试链接</h2>";
    
    if ($user222 && $_SESSION['ok'] == 1) {
        echo "<a href='check.php?username=222' class='btn btn-success' target='_blank'>🚀 查看用户222的内容</a>";
        echo "<a href='u.php?username=222' class='btn' target='_blank'>📝 编辑用户222的内容</a>";
    } elseif (!$user222) {
        echo "<span style='color:#999;'>用户222不存在，无法访问</span>";
    } else {
        echo "<a href='check.php?username=222' class='btn' target='_blank'>🔒 尝试访问（会重定向）</a>";
    }
    
    echo "<a href='check.php' class='btn'>👁️ 查看我的内容</a>";
    echo "<a href='test_users.php' class='btn'>👥 用户管理</a>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库错误：" . $e->getMessage() . "</div>";
}

echo "<hr>";
echo "<h2>💡 总结</h2>";
echo "<div class='info'>";
echo "<strong>管理员访问 check.php?username=222 的条件：</strong><br>";
echo "1. ✅ 已登录管理员账户 (ok=1)<br>";
echo "2. " . ($user222 ? "✅" : "❌") . " 用户222存在<br>";
echo "3. ✅ check.php 权限设置正确<br><br>";

if ($_SESSION['ok'] == 1 && $user222) {
    echo "<strong style='color:green;'>🎉 所有条件满足，您可以正常访问用户222的内容！</strong>";
} elseif ($_SESSION['ok'] != 1) {
    echo "<strong style='color:orange;'>⚠️ 需要管理员权限</strong>";
} elseif (!$user222) {
    echo "<strong style='color:red;'>❌ 需要先创建用户222</strong>";
}
echo "</div>";
?>
