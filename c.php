<?php
// 会员管理系统 - 管理控制台
// 深红色主题版本

require_once 'config.php';
check_permission(1); // 只允许管理员访问

$pdo = connectDB();

// 获取统计数据
$stats = [];
$stats['total_users'] = $pdo->query("SELECT COUNT(*) FROM tab")->fetchColumn();
$stats['admin_users'] = $pdo->query("SELECT COUNT(*) FROM tab WHERE ok = 1")->fetchColumn();
$stats['normal_users'] = $pdo->query("SELECT COUNT(*) FROM tab WHERE ok = 2")->fetchColumn();
$stats['total_content'] = $pdo->query("SELECT COUNT(*) FROM content")->fetchColumn();

// 获取当前发布的内容
$current_content = '';
$content_stmt = $pdo->query("SELECT content FROM content WHERE type = 'general' ORDER BY updated_time DESC LIMIT 1");
if ($content_row = $content_stmt->fetch()) {
    $current_content = $content_row['content'];
}

// 处理内容发布
$message = '';
if ($_POST && isset($_POST['content'])) {
    $new_content = $_POST['content'] ?? '';
    
    try {
        // 删除旧的通用内容
        $pdo->exec("DELETE FROM content WHERE type = 'general'");
        
        // 插入新内容
        if (!empty($new_content)) {
            $stmt = $pdo->prepare("INSERT INTO content (username, content, type) VALUES (?, ?, 'general')");
            $stmt->execute([$_SESSION['username'], $new_content]);
        }
        
        $message = '内容发布成功！';
        $current_content = $new_content;
    } catch (Exception $e) {
        $message = '发布失败：' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 管理控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            min-height: 100vh;
            color: #333;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            border-bottom: 3px solid #8B0000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #8B0000;
            font-size: 2.2em;
            font-weight: 700;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-badge {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .logout-btn {
            background: #dc3545;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .stat-card .icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .stat-card .number {
            font-size: 2.5em;
            font-weight: bold;
            color: #8B0000;
            margin-bottom: 10px;
        }
        
        .stat-card .label {
            color: #666;
            font-size: 1.1em;
            font-weight: 500;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-top: 30px;
        }
        
        .content-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .content-panel h2 {
            color: #8B0000;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #8B0000;
            padding-bottom: 10px;
        }
        
        .editor-container {
            margin-bottom: 20px;
        }
        
        .ckeditor-textarea {
            width: 100%;
            min-height: 300px;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            font-size: 14px;
            resize: vertical;
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 0, 0, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #20c997);
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .quick-actions {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .quick-actions h3 {
            color: #8B0000;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        
        .action-btn {
            display: block;
            width: 100%;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .current-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .current-content h3 {
            color: #8B0000;
            margin-bottom: 15px;
        }
        
        .content-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #8B0000;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🏛️ 管理控制台</h1>
            <div class="user-info">
                <div class="user-badge">👑 VIP管理员: <?php echo safe_output($_SESSION['username']); ?></div>
                <a href="close.php" class="logout-btn">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon">👥</div>
                <div class="number"><?php echo $stats['total_users']; ?></div>
                <div class="label">总会员数</div>
            </div>
            <div class="stat-card">
                <div class="icon">👑</div>
                <div class="number"><?php echo $stats['admin_users']; ?></div>
                <div class="label">管理员</div>
            </div>
            <div class="stat-card">
                <div class="icon">🎯</div>
                <div class="number"><?php echo $stats['normal_users']; ?></div>
                <div class="label">普通用户</div>
            </div>
            <div class="stat-card">
                <div class="icon">📝</div>
                <div class="number"><?php echo $stats['total_content']; ?></div>
                <div class="label">内容条数</div>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-success">✅ <?php echo safe_output($message); ?></div>
        <?php endif; ?>
        
        <div class="main-content">
            <div class="content-panel">
                <h2>📝 内容发布中心</h2>
                <form method="POST" action="">
                    <div class="editor-container">
                        <textarea name="content" id="editor" class="ckeditor-textarea" placeholder="在此输入要发布的内容..."><?php echo safe_output($current_content); ?></textarea>
                    </div>
                    <button type="submit" class="btn">🚀 发布内容</button>
                    <button type="button" class="btn btn-secondary" onclick="clearEditor()">🗑️ 清空内容</button>
                </form>
            </div>
            
            <div class="sidebar">
                <div class="quick-actions">
                    <h3>⚡ 快速操作</h3>
                    <a href="user.php" class="btn action-btn">👥 会员管理</a>
                    <a href="add.php" class="btn btn-success action-btn">➕ 添加会员</a>
                    <a href="backup.php" class="btn btn-info action-btn">💾 数据备份</a>
                    <a href="restore.php" class="btn btn-secondary action-btn">🔄 数据恢复</a>
                </div>
                
                <div class="current-content">
                    <h3>📄 当前内容预览</h3>
                    <div class="content-preview">
                        <?php if ($current_content): ?>
                            <?php echo $current_content; ?>
                        <?php else: ?>
                            <p style="color: #999; font-style: italic;">暂无发布内容</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="ckeditor/ckeditor.js"></script>
    <script>
        // 初始化CKEditor
        CKEDITOR.replace('editor', {
            height: 300,
            language: 'zh-cn',
            toolbar: [
                ['Bold', 'Italic', 'Underline', 'Strike'],
                ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent'],
                ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
                ['Link', 'Unlink'],
                ['TextColor', 'BGColor'],
                ['Font', 'FontSize'],
                ['Maximize', 'Source']
            ],
            removePlugins: 'elementspath',
            resize_enabled: false
        });

        // 清空编辑器
        function clearEditor() {
            if (confirm('确定要清空编辑器内容吗？')) {
                CKEDITOR.instances.editor.setData('');
            }
        }

        // 自动保存草稿
        setInterval(function() {
            const content = CKEDITOR.instances.editor.getData();
            if (content.trim()) {
                localStorage.setItem('draft_content', content);
            }
        }, 30000); // 每30秒保存一次

        // 页面加载时恢复草稿
        window.addEventListener('load', function() {
            const draft = localStorage.getItem('draft_content');
            if (draft && !CKEDITOR.instances.editor.getData().trim()) {
                if (confirm('发现未保存的草稿，是否恢复？')) {
                    CKEDITOR.instances.editor.setData(draft);
                }
            }
        });

        // 发布成功后清除草稿
        <?php if ($message && strpos($message, '成功') !== false): ?>
        localStorage.removeItem('draft_content');
        <?php endif; ?>
    </script>
</body>
</html>
