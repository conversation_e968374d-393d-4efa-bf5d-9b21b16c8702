<?php
// 会员管理系统 - 数据备份
// 深红色主题版本

require_once 'config.php';
check_permission(1); // 只允许管理员访问

$pdo = connectDB();
$message = '';
$error = '';

// 创建备份目录
$backup_dir = 'backups';
if (!is_dir($backup_dir)) {
    mkdir($backup_dir, 0755, true);
}

// 处理备份请求
if ($_POST && isset($_POST['create_backup'])) {
    try {
        $backup_filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        $backup_path = $backup_dir . '/' . $backup_filename;
        
        // 获取数据库配置
        global $db_host, $db_username, $db_password, $db_name;
        
        // 创建备份内容
        $backup_content = "-- 会员管理系统数据备份\n";
        $backup_content .= "-- 备份时间: " . date('Y-m-d H:i:s') . "\n";
        $backup_content .= "-- 备份人员: " . $_SESSION['username'] . "\n\n";
        
        $backup_content .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
        
        // 备份用户表
        $backup_content .= "-- 用户表备份\n";
        $backup_content .= "DROP TABLE IF EXISTS `tab`;\n";
        $backup_content .= "CREATE TABLE `tab` (\n";
        $backup_content .= "  `id` int(11) NOT NULL AUTO_INCREMENT,\n";
        $backup_content .= "  `username` varchar(50) NOT NULL,\n";
        $backup_content .= "  `password` varchar(255) NOT NULL,\n";
        $backup_content .= "  `ok` tinyint(1) NOT NULL DEFAULT 2,\n";
        $backup_content .= "  `regtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,\n";
        $backup_content .= "  PRIMARY KEY (`id`),\n";
        $backup_content .= "  UNIQUE KEY `username` (`username`)\n";
        $backup_content .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n\n";
        
        // 导出用户数据
        $users = $pdo->query("SELECT * FROM tab")->fetchAll();
        if ($users) {
            $backup_content .= "INSERT INTO `tab` (`id`, `username`, `password`, `ok`, `regtime`) VALUES\n";
            $values = [];
            foreach ($users as $user) {
                $values[] = sprintf("(%d, '%s', '%s', %d, '%s')",
                    $user['id'],
                    addslashes($user['username']),
                    addslashes($user['password']),
                    $user['ok'],
                    $user['regtime']
                );
            }
            $backup_content .= implode(",\n", $values) . ";\n\n";
        }
        
        // 备份内容表
        $backup_content .= "-- 内容表备份\n";
        $backup_content .= "DROP TABLE IF EXISTS `content`;\n";
        $backup_content .= "CREATE TABLE `content` (\n";
        $backup_content .= "  `id` int(11) NOT NULL AUTO_INCREMENT,\n";
        $backup_content .= "  `username` varchar(50) NOT NULL,\n";
        $backup_content .= "  `content` longtext NOT NULL,\n";
        $backup_content .= "  `type` varchar(20) NOT NULL DEFAULT 'general',\n";
        $backup_content .= "  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,\n";
        $backup_content .= "  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n";
        $backup_content .= "  PRIMARY KEY (`id`),\n";
        $backup_content .= "  KEY `username` (`username`),\n";
        $backup_content .= "  KEY `type` (`type`)\n";
        $backup_content .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n\n";
        
        // 导出内容数据
        $contents = $pdo->query("SELECT * FROM content")->fetchAll();
        if ($contents) {
            $backup_content .= "INSERT INTO `content` (`id`, `username`, `content`, `type`, `created_time`, `updated_time`) VALUES\n";
            $values = [];
            foreach ($contents as $content) {
                $values[] = sprintf("(%d, '%s', '%s', '%s', '%s', '%s')",
                    $content['id'],
                    addslashes($content['username']),
                    addslashes($content['content']),
                    addslashes($content['type']),
                    $content['created_time'],
                    $content['updated_time']
                );
            }
            $backup_content .= implode(",\n", $values) . ";\n\n";
        }
        
        $backup_content .= "SET FOREIGN_KEY_CHECKS=1;\n";
        
        // 写入备份文件
        if (file_put_contents($backup_path, $backup_content)) {
            $message = "备份创建成功！文件：$backup_filename";
            error_log("管理员 {$_SESSION['username']} 创建了数据备份: $backup_filename");
        } else {
            $error = '备份文件写入失败！';
        }
        
    } catch (Exception $e) {
        $error = '备份失败：' . $e->getMessage();
        error_log("备份错误: " . $e->getMessage());
    }
}

// 处理删除备份请求
if ($_POST && isset($_POST['delete_backup'])) {
    $filename = $_POST['filename'] ?? '';
    if ($filename && file_exists($backup_dir . '/' . $filename)) {
        if (unlink($backup_dir . '/' . $filename)) {
            $message = "备份文件 '$filename' 已删除！";
        } else {
            $error = '删除备份文件失败！';
        }
    } else {
        $error = '备份文件不存在！';
    }
}

// 处理批量删除
if ($_POST && isset($_POST['batch_delete'])) {
    $selected_files = $_POST['selected_files'] ?? [];
    $deleted_count = 0;
    
    foreach ($selected_files as $filename) {
        if (file_exists($backup_dir . '/' . $filename)) {
            if (unlink($backup_dir . '/' . $filename)) {
                $deleted_count++;
            }
        }
    }
    
    if ($deleted_count > 0) {
        $message = "成功删除 $deleted_count 个备份文件！";
    } else {
        $error = '没有文件被删除！';
    }
}

// 获取备份文件列表
$backup_files = [];
if (is_dir($backup_dir)) {
    $files = scandir($backup_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $filepath = $backup_dir . '/' . $file;
            $backup_files[] = [
                'name' => $file,
                'size' => filesize($filepath),
                'time' => filemtime($filepath),
                'path' => $filepath
            ];
        }
    }
    // 按时间倒序排列
    usort($backup_files, function($a, $b) {
        return $b['time'] - $a['time'];
    });
}

// 计算总大小
$total_size = array_sum(array_column($backup_files, 'size'));
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 数据备份</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            min-height: 100vh;
            color: #333;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            border-bottom: 3px solid #8B0000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #8B0000;
            font-size: 2.2em;
            font-weight: 700;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-links a {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .backup-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .panel-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .panel-header h2 {
            color: #8B0000;
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .panel-header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border-left: 5px solid #8B0000;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card .icon {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #8B0000;
        }
        
        .stat-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #8B0000;
            margin-bottom: 5px;
        }
        
        .stat-card .label {
            color: #666;
            font-size: 14px;
        }
        
        .backup-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(139, 0, 0, 0.4);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .files-table {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .table-header {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .batch-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .file-actions {
            display: flex;
            gap: 5px;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #8B0000, #DC143C);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .container {
                margin: 20px auto;
            }
            
            .backup-panel {
                padding: 20px;
            }
            
            .backup-actions {
                flex-direction: column;
            }
            
            .table-header {
                flex-direction: column;
                gap: 15px;
            }
            
            table {
                font-size: 14px;
            }
            
            th, td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>💾 数据备份</h1>
            <div class="nav-links">
                <a href="c.php">🏛️ 控制台</a>
                <a href="restore.php">🔄 数据恢复</a>
                <a href="user.php">👥 用户管理</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="backup-panel">
            <div class="panel-header">
                <h2>📊 备份统计</h2>
                <p>系统数据备份管理中心</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="icon">📁</div>
                    <div class="number"><?php echo count($backup_files); ?></div>
                    <div class="label">备份文件数</div>
                </div>
                <div class="stat-card">
                    <div class="icon">💽</div>
                    <div class="number"><?php echo number_format($total_size / 1024, 1); ?> KB</div>
                    <div class="label">总占用空间</div>
                </div>
                <div class="stat-card">
                    <div class="icon">👤</div>
                    <div class="number"><?php echo $pdo->query("SELECT COUNT(*) FROM tab")->fetchColumn(); ?></div>
                    <div class="label">用户数据</div>
                </div>
                <div class="stat-card">
                    <div class="icon">📝</div>
                    <div class="number"><?php echo $pdo->query("SELECT COUNT(*) FROM content")->fetchColumn(); ?></div>
                    <div class="label">内容数据</div>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    ❌ <?php echo safe_output($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    ✅ <?php echo safe_output($message); ?>
                </div>
            <?php endif; ?>
            
            <div class="backup-actions">
                <form method="POST" style="display: inline;">
                    <button type="submit" name="create_backup" class="btn" onclick="showProgress()">
                        🚀 创建新备份
                    </button>
                </form>
                <a href="restore.php" class="btn btn-secondary">🔄 数据恢复</a>
            </div>
            
            <div class="progress-bar" id="progressBar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
        
        <div class="files-table">
            <div class="table-header">
                <span>📋 备份文件列表</span>
                <div class="batch-controls">
                    <button onclick="selectAll()" class="btn btn-sm btn-secondary">全选</button>
                    <button onclick="batchDelete()" class="btn btn-sm btn-danger">批量删除</button>
                </div>
            </div>
            
            <?php if (empty($backup_files)): ?>
                <div style="padding: 60px; text-align: center; color: #999;">
                    <div style="font-size: 3em; margin-bottom: 20px;">📭</div>
                    <p>暂无备份文件</p>
                    <small>点击上方按钮创建第一个备份</small>
                </div>
            <?php else: ?>
                <form method="POST" id="batchForm">
                    <table>
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAllCheckbox" onchange="toggleAll()">
                                </th>
                                <th>文件名</th>
                                <th>大小</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($backup_files as $file): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" name="selected_files[]" value="<?php echo safe_output($file['name']); ?>" class="file-checkbox">
                                    </td>
                                    <td>
                                        <strong><?php echo safe_output($file['name']); ?></strong>
                                    </td>
                                    <td>
                                        <?php echo number_format($file['size'] / 1024, 1); ?> KB
                                    </td>
                                    <td>
                                        <?php echo date('Y-m-d H:i:s', $file['time']); ?>
                                    </td>
                                    <td>
                                        <div class="file-actions">
                                            <a href="<?php echo $file['path']; ?>" download class="btn btn-sm btn-success">
                                                ⬇️ 下载
                                            </a>
                                            <form method="POST" style="display: inline;" 
                                                  onsubmit="return confirm('确定要删除备份文件 <?php echo safe_output($file['name']); ?> 吗？')">
                                                <input type="hidden" name="filename" value="<?php echo safe_output($file['name']); ?>">
                                                <button type="submit" name="delete_backup" class="btn btn-sm btn-danger">
                                                    🗑️ 删除
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </form>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // 显示进度条
        function showProgress() {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            
            progressBar.style.display = 'block';
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                }
                progressFill.style.width = progress + '%';
            }, 200);
        }
        
        // 全选功能
        function selectAll() {
            const checkboxes = document.querySelectorAll('.file-checkbox');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            selectAllCheckbox.checked = true;
        }
        
        // 切换全选
        function toggleAll() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const checkboxes = document.querySelectorAll('.file-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        }
        
        // 批量删除
        function batchDelete() {
            const checkedBoxes = document.querySelectorAll('.file-checkbox:checked');
            
            if (checkedBoxes.length === 0) {
                alert('请先选择要删除的文件！');
                return;
            }
            
            if (confirm(`确定要删除选中的 ${checkedBoxes.length} 个备份文件吗？此操作不可恢复！`)) {
                const form = document.getElementById('batchForm');
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'batch_delete';
                input.value = '1';
                form.appendChild(input);
                form.submit();
            }
        }
        
        // 监听复选框变化
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('file-checkbox')) {
                const checkboxes = document.querySelectorAll('.file-checkbox');
                const checkedBoxes = document.querySelectorAll('.file-checkbox:checked');
                const selectAllCheckbox = document.getElementById('selectAllCheckbox');
                
                selectAllCheckbox.checked = checkboxes.length === checkedBoxes.length;
            }
        });
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
