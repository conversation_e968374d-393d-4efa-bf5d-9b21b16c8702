<?php
// 会员管理系统 - 添加会员
// 深红色主题版本

require_once 'config.php';
check_permission(1); // 只允许管理员访问

$pdo = connectDB();
$message = '';
$error = '';

// 处理添加会员请求
if ($_POST) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $user_type = (int)($_POST['user_type'] ?? 2);
    
    // 验证输入
    if (empty($username)) {
        $error = '用户名不能为空！';
    } elseif (strlen($username) < 3) {
        $error = '用户名至少需要3个字符！';
    } elseif (strlen($username) > 20) {
        $error = '用户名不能超过20个字符！';
    } elseif (!preg_match('/^[a-zA-Z0-9_\x{4e00}-\x{9fa5}]+$/u', $username)) {
        $error = '用户名只能包含字母、数字、下划线和中文！';
    } elseif (empty($password)) {
        $error = '密码不能为空！';
    } elseif (strlen($password) < 6) {
        $error = '密码至少需要6个字符！';
    } elseif ($password !== $confirm_password) {
        $error = '两次输入的密码不一致！';
    } elseif (!in_array($user_type, [1, 2])) {
        $error = '用户类型无效！';
    } else {
        try {
            // 检查用户名是否已存在
            $check_stmt = safe_query($pdo, "SELECT COUNT(*) FROM tab WHERE username = ?", [$username]);
            if ($check_stmt->fetchColumn() > 0) {
                $error = '用户名已存在，请选择其他用户名！';
            } else {
                // 创建新用户
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $insert_stmt = safe_query($pdo, 
                    "INSERT INTO tab (username, password, ok, regtime) VALUES (?, ?, ?, NOW())",
                    [$username, $hashed_password, $user_type]
                );
                
                if ($insert_stmt) {
                    $message = "会员 '$username' 添加成功！";
                    // 清空表单
                    $_POST = [];
                } else {
                    $error = '添加失败，请稍后重试！';
                }
            }
        } catch (Exception $e) {
            $error = '添加失败：' . $e->getMessage();
            error_log("添加用户错误: " . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 添加会员</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            min-height: 100vh;
            color: #333;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            border-bottom: 3px solid #8B0000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #8B0000;
            font-size: 2.2em;
            font-weight: 700;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-links a {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 0, 0, 0.3);
        }
        
        .container {
            max-width: 600px;
            margin: 50px auto;
            padding: 0 20px;
        }
        
        .form-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-header h2 {
            color: #8B0000;
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .form-header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #8B0000;
            box-shadow: 0 0 15px rgba(139, 0, 0, 0.2);
            background: rgba(255, 255, 255, 1);
            transform: scale(1.02);
        }
        
        .password-strength {
            margin-top: 5px;
            font-size: 12px;
            color: #666;
        }
        
        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #28a745; }
        
        .user-type-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
        }
        
        .type-option {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .type-option:hover {
            border-color: #8B0000;
            transform: translateY(-2px);
        }
        
        .type-option.selected {
            border-color: #8B0000;
            background: rgba(139, 0, 0, 0.1);
        }
        
        .type-option input[type="radio"] {
            display: none;
        }
        
        .type-option .icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .type-option .title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .type-option .desc {
            font-size: 12px;
            color: #666;
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 18px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(139, 0, 0, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            margin-top: 15px;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .form-tips {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            border-left: 4px solid #8B0000;
        }
        
        .form-tips h4 {
            color: #8B0000;
            margin-bottom: 10px;
        }
        
        .form-tips ul {
            margin-left: 20px;
            color: #666;
        }
        
        .form-tips li {
            margin-bottom: 5px;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .container {
                margin: 30px auto;
            }
            
            .form-panel {
                padding: 25px;
            }
            
            .user-type-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>➕ 添加会员</h1>
            <div class="nav-links">
                <a href="c.php">🏛️ 控制台</a>
                <a href="user.php">👥 会员管理</a>
                <a href="close.php">🚪 退出</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="form-panel">
            <div class="form-header">
                <h2>👤 创建新会员</h2>
                <p>为系统添加新的会员账户</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    ❌ <?php echo safe_output($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    ✅ <?php echo safe_output($message); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="" id="addUserForm">
                <div class="form-group">
                    <label for="username">用户名 *</label>
                    <input type="text" id="username" name="username" required 
                           value="<?php echo safe_output($_POST['username'] ?? ''); ?>"
                           placeholder="请输入用户名（3-20个字符）"
                           maxlength="20">
                </div>
                
                <div class="form-group">
                    <label for="password">密码 *</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="请输入密码（至少6个字符）"
                           minlength="6">
                    <div class="password-strength" id="passwordStrength"></div>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">确认密码 *</label>
                    <input type="password" id="confirm_password" name="confirm_password" required 
                           placeholder="请再次输入密码">
                </div>
                
                <div class="form-group">
                    <label>用户类型 *</label>
                    <div class="user-type-options">
                        <div class="type-option" onclick="selectUserType(2)">
                            <input type="radio" name="user_type" value="2" id="type_user" 
                                   <?php echo (!isset($_POST['user_type']) || $_POST['user_type'] == 2) ? 'checked' : ''; ?>>
                            <div class="icon">🎯</div>
                            <div class="title">普通用户</div>
                            <div class="desc">只能查看内容</div>
                        </div>
                        <div class="type-option" onclick="selectUserType(1)">
                            <input type="radio" name="user_type" value="1" id="type_admin"
                                   <?php echo (isset($_POST['user_type']) && $_POST['user_type'] == 1) ? 'checked' : ''; ?>>
                            <div class="icon">👑</div>
                            <div class="title">管理员</div>
                            <div class="desc">拥有所有权限</div>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="btn">🚀 创建会员</button>
                <button type="reset" class="btn btn-secondary">🔄 重置表单</button>
            </form>
            
            <div class="form-tips">
                <h4>📋 创建须知</h4>
                <ul>
                    <li>用户名只能包含字母、数字、下划线和中文字符</li>
                    <li>密码长度至少6个字符，建议包含字母和数字</li>
                    <li>管理员拥有所有系统权限，请谨慎分配</li>
                    <li>普通用户只能查看分配给他们的内容</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // 用户类型选择
        function selectUserType(type) {
            document.querySelectorAll('.type-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            if (type === 1) {
                document.getElementById('type_admin').checked = true;
                document.querySelector('[onclick="selectUserType(1)"]').classList.add('selected');
            } else {
                document.getElementById('type_user').checked = true;
                document.querySelector('[onclick="selectUserType(2)"]').classList.add('selected');
            }
        }
        
        // 初始化选中状态
        document.addEventListener('DOMContentLoaded', function() {
            const checkedType = document.querySelector('input[name="user_type"]:checked');
            if (checkedType) {
                selectUserType(parseInt(checkedType.value));
            }
        });
        
        // 密码强度检测
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthDiv.textContent = '';
                return;
            }
            
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            if (strength <= 2) {
                strengthDiv.textContent = '密码强度：弱';
                strengthDiv.className = 'password-strength strength-weak';
            } else if (strength <= 3) {
                strengthDiv.textContent = '密码强度：中等';
                strengthDiv.className = 'password-strength strength-medium';
            } else {
                strengthDiv.textContent = '密码强度：强';
                strengthDiv.className = 'password-strength strength-strong';
            }
        });
        
        // 确认密码验证
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('两次输入的密码不一致');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // 表单提交验证
        document.getElementById('addUserForm').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (username.length < 3) {
                e.preventDefault();
                alert('用户名至少需要3个字符！');
                return;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('密码至少需要6个字符！');
                return;
            }
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('两次输入的密码不一致！');
                return;
            }
        });
        
        // 重置表单时清除选中状态
        document.querySelector('button[type="reset"]').addEventListener('click', function() {
            setTimeout(() => {
                selectUserType(2); // 默认选择普通用户
                document.getElementById('passwordStrength').textContent = '';
            }, 10);
        });
    </script>
</body>
</html>
