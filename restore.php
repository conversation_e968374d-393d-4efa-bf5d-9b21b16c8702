<?php
// 会员管理系统 - 数据恢复
// 深红色主题版本

require_once 'config.php';
check_permission(1); // 只允许管理员访问

$pdo = connectDB();
$message = '';
$error = '';

// 备份目录
$backup_dir = 'backups';

// 处理文件上传恢复
if ($_POST && isset($_FILES['backup_file']) && $_FILES['backup_file']['error'] === UPLOAD_ERR_OK) {
    $uploaded_file = $_FILES['backup_file'];
    
    // 验证文件类型
    if (pathinfo($uploaded_file['name'], PATHINFO_EXTENSION) !== 'sql') {
        $error = '只能上传 .sql 格式的备份文件！';
    } else {
        try {
            $sql_content = file_get_contents($uploaded_file['tmp_name']);
            
            if (empty($sql_content)) {
                $error = '备份文件内容为空！';
            } else {
                // 执行恢复
                $pdo->exec("SET FOREIGN_KEY_CHECKS=0");
                
                // 分割SQL语句
                $statements = array_filter(array_map('trim', explode(';', $sql_content)));
                $executed = 0;
                
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^--/', $statement)) {
                        $pdo->exec($statement);
                        $executed++;
                    }
                }
                
                $pdo->exec("SET FOREIGN_KEY_CHECKS=1");
                
                $message = "数据恢复成功！执行了 $executed 条SQL语句。";
                error_log("管理员 {$_SESSION['username']} 从上传文件恢复了数据");
            }
        } catch (Exception $e) {
            $error = '数据恢复失败：' . $e->getMessage();
            error_log("数据恢复错误: " . $e->getMessage());
        }
    }
}

// 处理从现有备份恢复
if ($_POST && isset($_POST['restore_from_backup'])) {
    $backup_filename = $_POST['backup_filename'] ?? '';
    $backup_path = $backup_dir . '/' . $backup_filename;
    
    if (!file_exists($backup_path)) {
        $error = '备份文件不存在！';
    } else {
        try {
            $sql_content = file_get_contents($backup_path);
            
            if (empty($sql_content)) {
                $error = '备份文件内容为空！';
            } else {
                // 执行恢复
                $pdo->exec("SET FOREIGN_KEY_CHECKS=0");
                
                // 分割SQL语句
                $statements = array_filter(array_map('trim', explode(';', $sql_content)));
                $executed = 0;
                
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^--/', $statement)) {
                        $pdo->exec($statement);
                        $executed++;
                    }
                }
                
                $pdo->exec("SET FOREIGN_KEY_CHECKS=1");
                
                $message = "数据恢复成功！从备份文件 '$backup_filename' 执行了 $executed 条SQL语句。";
                error_log("管理员 {$_SESSION['username']} 从备份文件 $backup_filename 恢复了数据");
            }
        } catch (Exception $e) {
            $error = '数据恢复失败：' . $e->getMessage();
            error_log("数据恢复错误: " . $e->getMessage());
        }
    }
}

// 获取现有备份文件
$backup_files = [];
if (is_dir($backup_dir)) {
    $files = scandir($backup_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $filepath = $backup_dir . '/' . $file;
            $backup_files[] = [
                'name' => $file,
                'size' => filesize($filepath),
                'time' => filemtime($filepath)
            ];
        }
    }
    // 按时间倒序排列
    usort($backup_files, function($a, $b) {
        return $b['time'] - $a['time'];
    });
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 数据恢复</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            min-height: 100vh;
            color: #333;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            border-bottom: 3px solid #8B0000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #8B0000;
            font-size: 2.2em;
            font-weight: 700;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-links a {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }
        
        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .warning-panel {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .warning-panel .icon {
            font-size: 4em;
            color: #dc3545;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .warning-panel h2 {
            color: #dc3545;
            font-size: 2em;
            margin-bottom: 15px;
        }
        
        .warning-panel ul {
            text-align: left;
            margin: 20px auto;
            max-width: 600px;
            color: #856404;
        }
        
        .warning-panel li {
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .restore-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .method-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .method-header {
            text-align: center;
            margin-bottom: 25px;
        }
        
        .method-header .icon {
            font-size: 3em;
            margin-bottom: 15px;
            color: #8B0000;
        }
        
        .method-header h3 {
            color: #8B0000;
            font-size: 1.5em;
            margin-bottom: 10px;
        }
        
        .method-header p {
            color: #666;
            font-size: 14px;
        }
        
        .upload-area {
            border: 3px dashed #8B0000;
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            background: rgba(139, 0, 0, 0.05);
            border-color: #DC143C;
        }
        
        .upload-area.dragover {
            background: rgba(139, 0, 0, 0.1);
            border-color: #DC143C;
            transform: scale(1.02);
        }
        
        .upload-area input[type="file"] {
            display: none;
        }
        
        .upload-text {
            color: #666;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            color: #999;
            font-size: 12px;
        }
        
        .backup-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .backup-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .backup-item:hover {
            background: #f8f9fa;
        }
        
        .backup-item.selected {
            background: rgba(139, 0, 0, 0.1);
            border-left: 4px solid #8B0000;
        }
        
        .backup-item input[type="radio"] {
            margin-right: 10px;
        }
        
        .backup-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .backup-meta {
            font-size: 12px;
            color: #666;
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(139, 0, 0, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #8B0000, #DC143C);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .container {
                margin: 20px auto;
            }
            
            .restore-methods {
                grid-template-columns: 1fr;
            }
            
            .method-panel {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🔄 数据恢复</h1>
            <div class="nav-links">
                <a href="c.php">🏛️ 控制台</a>
                <a href="backup.php">💾 数据备份</a>
                <a href="user.php">👥 用户管理</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="warning-panel">
            <div class="icon">⚠️</div>
            <h2>危险操作警告</h2>
            <p style="color: #856404; font-size: 1.1em; margin-bottom: 20px;">
                数据恢复将<strong>完全覆盖</strong>现有数据，请务必谨慎操作！
            </p>
            <ul>
                <li><strong>所有现有用户数据将被删除</strong></li>
                <li><strong>所有现有内容数据将被清空</strong></li>
                <li><strong>恢复后的数据无法与现有数据合并</strong></li>
                <li><strong>建议在恢复前先创建当前数据的备份</strong></li>
                <li><strong>确保备份文件来源可靠且完整</strong></li>
            </ul>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-error">
                ❌ <?php echo safe_output($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($message): ?>
            <div class="alert alert-success">
                ✅ <?php echo safe_output($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="restore-methods">
            <div class="method-panel">
                <div class="method-header">
                    <div class="icon">📤</div>
                    <h3>上传备份文件</h3>
                    <p>从本地上传 .sql 备份文件进行恢复</p>
                </div>
                
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                        <input type="file" id="fileInput" name="backup_file" accept=".sql" onchange="handleFileSelect(this)">
                        <div class="upload-text">
                            <strong>点击选择文件</strong> 或 拖拽文件到此处
                        </div>
                        <div class="upload-hint">
                            只支持 .sql 格式的备份文件
                        </div>
                    </div>
                    
                    <div id="fileInfo" style="display: none; margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px;">
                        <div id="fileName" style="font-weight: bold; color: #333;"></div>
                        <div id="fileSize" style="font-size: 12px; color: #666;"></div>
                    </div>
                    
                    <button type="submit" class="btn btn-danger" id="uploadBtn" disabled 
                            onclick="return confirm('确定要从上传的文件恢复数据吗？这将覆盖所有现有数据！')">
                        🚀 开始恢复
                    </button>
                </form>
            </div>
            
            <div class="method-panel">
                <div class="method-header">
                    <div class="icon">📁</div>
                    <h3>从现有备份恢复</h3>
                    <p>从服务器上的备份文件中选择恢复</p>
                </div>
                
                <?php if (empty($backup_files)): ?>
                    <div style="text-align: center; padding: 40px; color: #999;">
                        <div style="font-size: 3em; margin-bottom: 15px;">📭</div>
                        <p>暂无可用的备份文件</p>
                        <small>请先创建备份或上传备份文件</small>
                    </div>
                <?php else: ?>
                    <form method="POST" id="restoreForm">
                        <div class="backup-list">
                            <?php foreach ($backup_files as $index => $file): ?>
                                <div class="backup-item" onclick="selectBackup('<?php echo safe_output($file['name']); ?>', this)">
                                    <input type="radio" name="backup_filename" value="<?php echo safe_output($file['name']); ?>" 
                                           id="backup_<?php echo $index; ?>" <?php echo $index === 0 ? 'checked' : ''; ?>>
                                    <div class="backup-name"><?php echo safe_output($file['name']); ?></div>
                                    <div class="backup-meta">
                                        大小: <?php echo number_format($file['size'] / 1024, 1); ?> KB | 
                                        时间: <?php echo date('Y-m-d H:i:s', $file['time']); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <button type="submit" name="restore_from_backup" class="btn btn-danger" 
                                onclick="return confirm('确定要从选中的备份文件恢复数据吗？这将覆盖所有现有数据！')">
                            🔄 恢复数据
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="progress-bar" id="progressBar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
    </div>
    
    <script>
        // 文件选择处理
        function handleFileSelect(input) {
            const file = input.files[0];
            const fileInfo = document.getElementById('fileInfo');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');
            const uploadBtn = document.getElementById('uploadBtn');
            
            if (file) {
                if (file.name.toLowerCase().endsWith('.sql')) {
                    fileName.textContent = '📄 ' + file.name;
                    fileSize.textContent = '大小: ' + (file.size / 1024).toFixed(1) + ' KB';
                    fileInfo.style.display = 'block';
                    uploadBtn.disabled = false;
                    uploadBtn.style.opacity = '1';
                } else {
                    alert('请选择 .sql 格式的备份文件！');
                    input.value = '';
                    fileInfo.style.display = 'none';
                    uploadBtn.disabled = true;
                    uploadBtn.style.opacity = '0.5';
                }
            } else {
                fileInfo.style.display = 'none';
                uploadBtn.disabled = true;
                uploadBtn.style.opacity = '0.5';
            }
        }
        
        // 拖拽上传
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const fileInput = document.getElementById('fileInput');
                fileInput.files = files;
                handleFileSelect(fileInput);
            }
        });
        
        // 选择备份文件
        function selectBackup(filename, element) {
            document.querySelectorAll('.backup-item').forEach(item => {
                item.classList.remove('selected');
            });
            element.classList.add('selected');
            
            const radio = element.querySelector('input[type="radio"]');
            radio.checked = true;
        }
        
        // 初始化选中第一个备份
        document.addEventListener('DOMContentLoaded', function() {
            const firstBackup = document.querySelector('.backup-item');
            if (firstBackup) {
                firstBackup.classList.add('selected');
            }
        });
        
        // 显示进度条
        function showProgress() {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            
            progressBar.style.display = 'block';
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                }
                progressFill.style.width = progress + '%';
            }, 300);
        }
        
        // 表单提交时显示进度
        document.getElementById('uploadForm')?.addEventListener('submit', showProgress);
        document.getElementById('restoreForm')?.addEventListener('submit', showProgress);
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const panels = document.querySelectorAll('.method-panel');
            panels.forEach((panel, index) => {
                panel.style.opacity = '0';
                panel.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    panel.style.transition = 'all 0.6s ease';
                    panel.style.opacity = '1';
                    panel.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
