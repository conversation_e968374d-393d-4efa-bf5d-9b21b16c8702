<?php
// 测试 user.php 页面访问权限
session_start();
require_once 'config.php';

echo "<h1>🔍 user.php 访问权限测试</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .info { background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .success { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; color: #2e7d32; }
    .error { background: #ffebee; padding: 15px; border-radius: 8px; margin: 10px 0; color: #c62828; }
    .warning { background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; color: #856404; }
    .btn { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }
    .btn:hover { background: #005a8b; }
    .btn-success { background: #28a745; }
    .btn-danger { background: #dc3545; }
    .btn-warning { background: #ffc107; color: #212529; }
    table { border-collapse: collapse; width: 100%; margin: 15px 0; }
    th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
    th { background: #f2f2f2; font-weight: bold; }
</style>";

// 检查登录状态
if (!isset($_SESSION['username'])) {
    echo "<div class='error'>❌ 未登录！请先登录系统。</div>";
    echo "<a href='index.php' class='btn'>🔑 前往登录</a>";
    exit();
}

echo "<div class='info'>";
echo "<h2>📋 当前登录信息</h2>";
echo "<strong>用户名：</strong>" . htmlspecialchars($_SESSION['username']) . "<br>";
echo "<strong>权限级别：</strong>" . ($_SESSION['ok'] == 1 ? '👑 管理员' : '🎯 普通用户') . " (ok=" . $_SESSION['ok'] . ")<br>";
echo "<strong>Session ID：</strong>" . session_id() . "<br>";
echo "</div>";

// 权限检查
echo "<h2>🔐 user.php 访问权限分析</h2>";

if ($_SESSION['ok'] == 1) {
    echo "<div class='success'>";
    echo "<strong>🎉 您是管理员，有权限访问 user.php 页面！</strong><br>";
    echo "user.php 第6行设置：check_permission(1) - 只允许管理员访问<br>";
    echo "您的权限级别：ok=" . $_SESSION['ok'] . " (管理员)<br>";
    echo "结论：✅ 可以正常访问用户管理页面<br>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<strong>❌ 您是普通用户，无权访问 user.php 页面！</strong><br>";
    echo "user.php 要求管理员权限 (ok=1)<br>";
    echo "您的权限级别：ok=" . $_SESSION['ok'] . " (普通用户)<br>";
    echo "访问 user.php 会被重定向或显示权限不足错误<br>";
    echo "</div>";
}

try {
    $pdo = connectDB();
    
    // 获取用户统计
    $total_users = $pdo->query("SELECT COUNT(*) FROM tab")->fetchColumn();
    $admin_users = $pdo->query("SELECT COUNT(*) FROM tab WHERE ok = 1")->fetchColumn();
    $normal_users = $pdo->query("SELECT COUNT(*) FROM tab WHERE ok = 2")->fetchColumn();
    
    echo "<h2>📊 系统用户统计</h2>";
    echo "<div class='info'>";
    echo "<strong>总用户数：</strong>" . $total_users . "<br>";
    echo "<strong>管理员：</strong>" . $admin_users . " 人<br>";
    echo "<strong>普通用户：</strong>" . $normal_users . " 人<br>";
    echo "</div>";
    
    // 显示所有用户（如果是管理员）
    if ($_SESSION['ok'] == 1) {
        echo "<h2>👥 用户列表预览</h2>";
        $users = $pdo->query("SELECT * FROM tab ORDER BY id LIMIT 10")->fetchAll();
        
        echo "<table>";
        echo "<tr><th>ID</th><th>用户名</th><th>权限</th><th>注册时间</th><th>操作预览</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . ($user['ok'] == 1 ? '👑 管理员' : '🎯 普通用户') . "</td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($user['regtime'])) . "</td>";
            echo "<td>";
            echo "<span style='font-size:12px;color:#666;'>编辑 | 删除 | 查看</span>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if ($total_users > 10) {
            echo "<p style='color:#666;font-style:italic;'>显示前10个用户，完整列表请访问 user.php</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库错误：" . $e->getMessage() . "</div>";
}

// 功能测试链接
echo "<h2>🔗 功能测试</h2>";

if ($_SESSION['ok'] == 1) {
    echo "<div class='success'>";
    echo "<strong>管理员功能链接：</strong><br>";
    echo "<a href='user.php' class='btn btn-success' target='_blank'>🚀 访问用户管理页面</a>";
    echo "<a href='add.php' class='btn' target='_blank'>➕ 添加新用户</a>";
    echo "<a href='c.php' class='btn' target='_blank'>🏛️ 管理控制台</a>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<strong>user.php 页面功能：</strong><br>";
    echo "• 👥 查看所有用户列表<br>";
    echo "• 🔍 搜索和筛选用户<br>";
    echo "• 📄 分页显示用户<br>";
    echo "• ✏️ 编辑用户内容<br>";
    echo "• 🗑️ 删除用户<br>";
    echo "• 🗂️ 清空用户内容<br>";
    echo "• 👁️ 查看用户内容<br>";
    echo "</div>";
    
} else {
    echo "<div class='warning'>";
    echo "<strong>普通用户限制：</strong><br>";
    echo "• ❌ 无法访问 user.php<br>";
    echo "• ❌ 无法管理其他用户<br>";
    echo "• ✅ 只能访问自己的内容<br>";
    echo "</div>";
    
    echo "<a href='e.php' class='btn'>🏠 用户中心</a>";
    echo "<a href='check.php' class='btn'>👁️ 查看我的内容</a>";
}

// 权限提升说明
if ($_SESSION['ok'] != 1) {
    echo "<h2>🔧 如何获得管理员权限</h2>";
    echo "<div class='warning'>";
    echo "<strong>注意：</strong>只有现有管理员才能提升其他用户的权限<br>";
    echo "1. 联系现有管理员<br>";
    echo "2. 请求将您的账户权限从 ok=2 改为 ok=1<br>";
    echo "3. 或者使用管理员账户登录<br>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<strong>默认管理员账户：</strong><br>";
    echo "用户名：admin<br>";
    echo "密码：admin123<br>";
    echo "<a href='close.php' class='btn btn-warning'>🚪 切换账户</a>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>💡 总结</h2>";
echo "<div class='info'>";
echo "<strong>user.php 访问要求：</strong><br>";
echo "1. " . (isset($_SESSION['username']) ? "✅" : "❌") . " 已登录系统<br>";
echo "2. " . ($_SESSION['ok'] == 1 ? "✅" : "❌") . " 管理员权限 (ok=1)<br>";
echo "3. ✅ 数据库连接正常<br><br>";

if ($_SESSION['ok'] == 1) {
    echo "<strong style='color:green;'>🎉 所有条件满足，您可以正常访问 user.php 页面！</strong>";
} else {
    echo "<strong style='color:red;'>❌ 需要管理员权限才能访问 user.php</strong>";
}
echo "</div>";

// 快速诊断
echo "<h2>🔍 快速诊断</h2>";
echo "<div class='info'>";
echo "<strong>如果无法访问 user.php，可能的原因：</strong><br>";
echo "• Session 过期或损坏 - 重新登录<br>";
echo "• 权限不足 - 使用管理员账户<br>";
echo "• 数据库连接问题 - 检查配置<br>";
echo "• 浏览器缓存问题 - 清除缓存<br>";
echo "</div>";

echo "<div style='margin-top:20px;'>";
echo "<a href='debug.php' class='btn'>🔍 系统诊断</a>";
echo "<a href='test_check_access.php' class='btn'>🔐 权限测试</a>";
echo "</div>";
?>
