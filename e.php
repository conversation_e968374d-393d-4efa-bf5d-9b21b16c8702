<?php
// 会员管理系统 - 用户主页
// 深红色主题版本

require_once 'config.php';
check_permission(2); // 允许普通用户和管理员访问

$pdo = connectDB();

// 获取当前用户信息
$user_stmt = safe_query($pdo, "SELECT * FROM tab WHERE username = ?", [$_SESSION['username']]);
$current_user = $user_stmt->fetch();

// 获取通用内容
$general_content = '';
$general_stmt = safe_query($pdo, "SELECT content FROM content WHERE type = 'general' ORDER BY updated_time DESC LIMIT 1");
if ($general_row = $general_stmt->fetch()) {
    $general_content = $general_row['content'];
}

// 获取个人专属内容
$personal_content = '';
$personal_stmt = safe_query($pdo, "SELECT content FROM content WHERE username = ? AND type = 'personal' ORDER BY updated_time DESC LIMIT 1", [$_SESSION['username']]);
if ($personal_row = $personal_stmt->fetch()) {
    $personal_content = $personal_row['content'];
}

// 获取系统统计
$total_users = $pdo->query("SELECT COUNT(*) FROM tab")->fetchColumn();
$total_content = $pdo->query("SELECT COUNT(*) FROM content")->fetchColumn();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 用户中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 25s ease infinite;
            min-height: 100vh;
            color: #333;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 25px 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            border-bottom: 3px solid #8B0000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #8B0000;
            font-size: 2.5em;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-badge {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 16px;
            box-shadow: 0 5px 15px rgba(139, 0, 0, 0.3);
        }
        
        .logout-btn {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 12px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }
        
        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(220, 53, 69, 0.4);
        }
        
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }
        
        .welcome-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
        }
        
        .welcome-section h2 {
            color: #8B0000;
            font-size: 2.2em;
            margin-bottom: 15px;
        }
        
        .welcome-section p {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 25px;
        }
        
        .user-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border-left: 5px solid #8B0000;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card .icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .stat-card .label {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .stat-card .value {
            color: #8B0000;
            font-size: 1.8em;
            font-weight: bold;
        }
        
        .content-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .content-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .content-panel h3 {
            color: #8B0000;
            font-size: 1.5em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #8B0000;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .content-display {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 4px solid #8B0000;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            line-height: 1.6;
        }
        
        .empty-content {
            color: #999;
            font-style: italic;
            text-align: center;
            padding: 60px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }
        
        .empty-content .icon {
            font-size: 3em;
            opacity: 0.5;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(139, 0, 0, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }
        
        .system-info {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .system-info h3 {
            color: #8B0000;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #8B0000;
        }
        
        .info-item .icon {
            font-size: 1.5em;
            color: #8B0000;
        }
        
        .info-item .details {
            flex: 1;
        }
        
        .info-item .label {
            color: #666;
            font-size: 14px;
            margin-bottom: 3px;
        }
        
        .info-item .value {
            color: #333;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .container {
                margin: 20px auto;
            }
            
            .welcome-section {
                padding: 25px;
            }
            
            .content-section {
                grid-template-columns: 1fr;
            }
            
            .user-stats {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🏠 用户中心</h1>
            <div class="user-info">
                <div class="user-badge">
                    <?php if ($current_user['ok'] == 1): ?>
                        👑 VIP管理员: <?php echo safe_output($_SESSION['username']); ?>
                    <?php else: ?>
                        🎯 尊贵会员: <?php echo safe_output($_SESSION['username']); ?>
                    <?php endif; ?>
                </div>
                <a href="close.php" class="logout-btn">🚪 退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="welcome-section">
            <h2>🎉 欢迎回来！</h2>
            <p>尊敬的<?php echo $current_user['ok'] == 1 ? 'VIP管理员' : '会员'; ?>，欢迎使用<?php echo SYSTEM_NAME; ?></p>
            
            <div class="user-stats">
                <div class="stat-card">
                    <div class="icon">👤</div>
                    <div class="label">会员身份</div>
                    <div class="value">
                        <?php echo $current_user['ok'] == 1 ? 'VIP管理员' : '尊贵会员'; ?>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="icon">📅</div>
                    <div class="label">加入时间</div>
                    <div class="value"><?php echo date('Y-m-d', strtotime($current_user['regtime'])); ?></div>
                </div>
                <div class="stat-card">
                    <div class="icon">⏰</div>
                    <div class="label">会员天数</div>
                    <div class="value"><?php echo ceil((time() - strtotime($current_user['regtime'])) / 86400); ?> 天</div>
                </div>
                <div class="stat-card">
                    <div class="icon">🆔</div>
                    <div class="label">会员编号</div>
                    <div class="value">#<?php echo str_pad($current_user['id'], 6, '0', STR_PAD_LEFT); ?></div>
                </div>
            </div>
        </div>
        
        <div class="content-section">
            <div class="content-panel">
                <h3>📢 系统公告</h3>
                <div class="content-display">
                    <?php if ($general_content): ?>
                        <?php echo $general_content; ?>
                    <?php else: ?>
                        <div class="empty-content">
                            <div class="icon">📭</div>
                            <p>暂无系统公告</p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="action-buttons">
                    <a href="check.php" class="btn btn-info">📄 查看详情</a>
                    <?php if ($current_user['ok'] == 1): ?>
                        <a href="c.php" class="btn">🏛️ 管理控制台</a>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="content-panel">
                <h3>🎯 专属内容</h3>
                <div class="content-display">
                    <?php if ($personal_content): ?>
                        <?php echo $personal_content; ?>
                    <?php else: ?>
                        <div class="empty-content">
                            <div class="icon">🎁</div>
                            <p>暂无专属内容</p>
                            <small>管理员可为您分配专属内容</small>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="action-buttons">
                    <a href="check.php?username=<?php echo urlencode($_SESSION['username']); ?>" class="btn btn-secondary">👁️ 查看专属</a>
                    <?php if ($current_user['ok'] == 1): ?>
                        <a href="u.php?username=<?php echo urlencode($_SESSION['username']); ?>" class="btn">✏️ 编辑内容</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="system-info">
            <h3>📊 系统信息</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="icon">🌐</div>
                    <div class="details">
                        <div class="label">系统名称</div>
                        <div class="value"><?php echo SYSTEM_NAME; ?></div>
                    </div>
                </div>
                <div class="info-item">
                    <div class="icon">🔢</div>
                    <div class="details">
                        <div class="label">系统版本</div>
                        <div class="value">v<?php echo VERSION; ?></div>
                    </div>
                </div>
                <div class="info-item">
                    <div class="icon">👥</div>
                    <div class="details">
                        <div class="label">总会员数</div>
                        <div class="value"><?php echo $total_users; ?> 人</div>
                    </div>
                </div>
                <div class="info-item">
                    <div class="icon">📝</div>
                    <div class="details">
                        <div class="label">内容总数</div>
                        <div class="value"><?php echo $total_content; ?> 条</div>
                    </div>
                </div>
                <div class="info-item">
                    <div class="icon">🕒</div>
                    <div class="details">
                        <div class="label">当前时间</div>
                        <div class="value"><?php echo date('Y-m-d H:i:s'); ?></div>
                    </div>
                </div>
                <div class="info-item">
                    <div class="icon">🌍</div>
                    <div class="details">
                        <div class="label">服务器</div>
                        <div class="value"><?php echo $_SERVER['SERVER_NAME']; ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .content-panel, .info-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
        
        // 实时时间更新
        function updateTime() {
            const timeElements = document.querySelectorAll('.info-item .value');
            const now = new Date();
            const timeString = now.getFullYear() + '-' + 
                             String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                             String(now.getDate()).padStart(2, '0') + ' ' +
                             String(now.getHours()).padStart(2, '0') + ':' + 
                             String(now.getMinutes()).padStart(2, '0') + ':' + 
                             String(now.getSeconds()).padStart(2, '0');
            
            timeElements.forEach(element => {
                if (element.parentElement.querySelector('.label').textContent === '当前时间') {
                    element.textContent = timeString;
                }
            });
        }
        
        // 每秒更新时间
        setInterval(updateTime, 1000);
        
        // 内容区域滚动优化
        document.querySelectorAll('.content-display').forEach(element => {
            element.addEventListener('scroll', function() {
                if (this.scrollTop > 0) {
                    this.style.boxShadow = 'inset 0 10px 10px -10px rgba(0,0,0,0.1)';
                } else {
                    this.style.boxShadow = 'none';
                }
            });
        });
    </script>
</body>
</html>
