<?php
// 会员管理系统 - 会员管理
// 深红色主题版本

require_once 'config.php';
check_permission(1); // 只允许管理员访问

$pdo = connectDB();

// 处理搜索和筛选
$search = $_GET['search'] ?? '';
$filter = $_GET['filter'] ?? 'all';

// 构建查询条件
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "username LIKE ?";
    $params[] = "%$search%";
}

if ($filter !== 'all') {
    $where_conditions[] = "ok = ?";
    $params[] = ($filter === 'admin') ? 1 : 2;
}

$where_clause = empty($where_conditions) ? '' : 'WHERE ' . implode(' AND ', $where_conditions);

// 分页设置
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// 获取总数
$count_sql = "SELECT COUNT(*) FROM tab $where_clause";
$total_users = $pdo->prepare($count_sql);
$total_users->execute($params);
$total_count = $total_users->fetchColumn();
$total_pages = ceil($total_count / $per_page);

// 获取用户列表
$sql = "SELECT * FROM tab $where_clause ORDER BY regtime DESC LIMIT $per_page OFFSET $offset";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$users = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 会员管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            min-height: 100vh;
            color: #333;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            border-bottom: 3px solid #8B0000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #8B0000;
            font-size: 2.2em;
            font-weight: 700;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-links a {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 0, 0, 0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .search-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin-bottom: 30px;
        }
        
        .search-form {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-form input {
            flex: 1;
            min-width: 200px;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
        }
        
        .search-form select {
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            background: white;
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 0, 0, 0.3);
        }
        
        .users-table {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .table-header {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .table-content {
            padding: 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .user-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        
        .badge-admin {
            background: linear-gradient(135deg, #8B0000, #DC143C);
        }
        
        .badge-user {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .pagination a, .pagination span {
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            text-decoration: none;
            color: #333;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover {
            background: #8B0000;
            color: white;
            transform: translateY(-2px);
        }
        
        .pagination .current {
            background: #8B0000;
            color: white;
        }
        
        .stats-info {
            text-align: center;
            margin-bottom: 20px;
            color: white;
            font-size: 1.1em;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .search-form {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-form input, .search-form select {
                min-width: auto;
            }
            
            table {
                font-size: 14px;
            }
            
            th, td {
                padding: 10px 8px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>👥 会员管理</h1>
            <div class="nav-links">
                <a href="c.php">🏛️ 控制台</a>
                <a href="add.php">➕ 添加会员</a>
                <a href="close.php">🚪 退出</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="stats-info">
            📊 共找到 <?php echo $total_count; ?> 位会员 | 第 <?php echo $page; ?> 页，共 <?php echo $total_pages; ?> 页
        </div>
        
        <div class="search-panel">
            <form method="GET" class="search-form">
                <input type="text" name="search" placeholder="搜索用户名..." value="<?php echo safe_output($search); ?>">
                <select name="filter">
                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>全部用户</option>
                    <option value="admin" <?php echo $filter === 'admin' ? 'selected' : ''; ?>>管理员</option>
                    <option value="user" <?php echo $filter === 'user' ? 'selected' : ''; ?>>普通用户</option>
                </select>
                <button type="submit" class="btn">🔍 搜索</button>
                <a href="user.php" class="btn" style="background: #6c757d;">🔄 重置</a>
            </form>
        </div>
        
        <div class="users-table">
            <div class="table-header">
                👥 会员列表管理
            </div>
            <div class="table-content">
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>权限等级</th>
                            <th>注册时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($users)): ?>
                            <tr>
                                <td colspan="5" style="text-align: center; padding: 40px; color: #999;">
                                    😔 暂无符合条件的会员
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo $user['id']; ?></td>
                                    <td>
                                        <strong><?php echo safe_output($user['username']); ?></strong>
                                        <?php if ($user['username'] === $_SESSION['username']): ?>
                                            <span style="color: #8B0000; font-size: 12px;">(当前用户)</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['ok'] == 1): ?>
                                            <span class="user-badge badge-admin">👑 管理员</span>
                                        <?php else: ?>
                                            <span class="user-badge badge-user">🎯 普通用户</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($user['regtime'])); ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <?php if ($user['username'] !== $_SESSION['username']): ?>
                                                <a href="del.php?username=<?php echo urlencode($user['username']); ?>" 
                                                   class="btn btn-sm btn-danger"
                                                   onclick="return confirm('确定要删除用户 <?php echo safe_output($user['username']); ?> 吗？')">
                                                    🗑️ 删除
                                                </a>
                                            <?php endif; ?>
                                            <a href="u.php?username=<?php echo urlencode($user['username']); ?>" 
                                               class="btn btn-sm btn-info">
                                                📝 指定内容
                                            </a>
                                            <a href="udel.php?username=<?php echo urlencode($user['username']); ?>" 
                                               class="btn btn-sm btn-warning"
                                               onclick="return confirm('确定要清空 <?php echo safe_output($user['username']); ?> 的个人内容吗？')">
                                                🗂️ 清空内容
                                            </a>
                                            <a href="check.php?username=<?php echo urlencode($user['username']); ?>" 
                                               class="btn btn-sm btn-success">
                                                👁️ 查看内容
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=1&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>">首页</a>
                    <a href="?page=<?php echo $page-1; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>">上一页</a>
                <?php endif; ?>
                
                <?php
                $start = max(1, $page - 2);
                $end = min($total_pages, $page + 2);
                for ($i = $start; $i <= $end; $i++):
                ?>
                    <?php if ($i == $page): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page+1; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>">下一页</a>
                    <a href="?page=<?php echo $total_pages; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>">末页</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
