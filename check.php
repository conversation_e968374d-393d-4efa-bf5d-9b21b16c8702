<?php
// 会员管理系统 - 内容查看
// 深红色主题版本

require_once 'config.php';
check_permission(2); // 允许普通用户和管理员访问

$pdo = connectDB();

// 获取查看目标用户名（如果指定）
$target_username = $_GET['username'] ?? $_SESSION['username'];

// 如果不是管理员，只能查看自己的内容
if ($_SESSION['ok'] != 1 && $target_username !== $_SESSION['username']) {
    $target_username = $_SESSION['username'];
}

// 获取目标用户信息
$user_stmt = safe_query($pdo, "SELECT * FROM tab WHERE username = ?", [$target_username]);
$target_user = $user_stmt->fetch();
if (!$target_user) {
    header('Location: e.php');
    exit();
}

// 获取通用内容
$general_content = '';
$general_stmt = safe_query($pdo, "SELECT content, updated_time FROM content WHERE type = 'general' ORDER BY updated_time DESC LIMIT 1");
if ($general_row = $general_stmt->fetch()) {
    $general_content = $general_row['content'];
    $general_updated = $general_row['updated_time'];
}

// 获取个人专属内容
$personal_content = '';
$personal_updated = '';
$personal_stmt = safe_query($pdo, "SELECT content, updated_time FROM content WHERE username = ? AND type = 'personal' ORDER BY updated_time DESC LIMIT 1", [$target_username]);
if ($personal_row = $personal_stmt->fetch()) {
    $personal_content = $personal_row['content'];
    $personal_updated = $personal_row['updated_time'];
}

// 确定显示模式
$view_mode = $_GET['mode'] ?? 'all'; // all, general, personal
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 内容查看</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 25s ease infinite;
            min-height: 100vh;
            color: #333;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            border-bottom: 3px solid #8B0000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #8B0000;
            font-size: 2.2em;
            font-weight: 700;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-links a {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }
        
        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .user-info-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .user-info-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #8B0000, #DC143C);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            font-weight: bold;
        }
        
        .user-details h2 {
            color: #8B0000;
            font-size: 1.8em;
            margin-bottom: 5px;
        }
        
        .user-badge {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .user-meta {
            color: #666;
            font-size: 14px;
        }
        
        .view-controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .view-controls label {
            font-weight: bold;
            color: #333;
        }
        
        .view-btn {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .view-btn.active {
            background: linear-gradient(135deg, #8B0000, #DC143C);
        }
        
        .view-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .content-container {
            display: grid;
            gap: 30px;
        }
        
        .content-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #8B0000;
        }
        
        .content-header h3 {
            color: #8B0000;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .content-meta {
            color: #666;
            font-size: 12px;
            text-align: right;
        }
        
        .content-body {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #8B0000;
            min-height: 200px;
            line-height: 1.8;
            font-size: 16px;
        }
        
        .empty-content {
            color: #999;
            font-style: italic;
            text-align: center;
            padding: 60px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }
        
        .empty-content .icon {
            font-size: 4em;
            opacity: 0.5;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(139, 0, 0, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }
        
        .print-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }
        
        .print-btn {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #8B0000;
            color: #8B0000;
            padding: 10px 15px;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .print-btn:hover {
            background: #8B0000;
            color: white;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .container {
                margin: 20px auto;
            }
            
            .user-info-header {
                flex-direction: column;
                text-align: center;
            }
            
            .view-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .content-header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .print-controls {
                position: static;
                justify-content: center;
                margin-bottom: 20px;
            }
        }
        
        @media print {
            body {
                background: white !important;
                color: black !important;
            }
            
            .header, .nav-links, .view-controls, .action-buttons, .print-controls {
                display: none !important;
            }
            
            .content-panel {
                background: white !important;
                box-shadow: none !important;
                border: 1px solid #ccc !important;
                margin-bottom: 20px !important;
            }
            
            .content-body {
                background: white !important;
                border-left: 3px solid #333 !important;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>📄 内容查看</h1>
            <div class="nav-links">
                <a href="e.php">🏠 返回主页</a>
                <?php if ($_SESSION['ok'] == 1): ?>
                    <a href="c.php">🏛️ 控制台</a>
                    <a href="user.php">👥 用户管理</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="print-controls">
        <button class="print-btn" onclick="window.print()">🖨️ 打印</button>
        <button class="print-btn" onclick="copyContent()">📋 复制</button>
    </div>
    
    <div class="container">
        <div class="user-info-panel">
            <div class="user-info-header">
                <div class="user-avatar">
                    <?php echo mb_substr($target_user['username'], 0, 1, 'UTF-8'); ?>
                </div>
                <div class="user-details">
                    <h2><?php echo safe_output($target_user['username']); ?></h2>
                    <div class="user-badge">
                        <?php if ($target_user['ok'] == 1): ?>
                            👑 VIP管理员
                        <?php else: ?>
                            🎯 尊贵会员
                        <?php endif; ?>
                    </div>
                    <div class="user-meta">
                        📅 加入时间：<?php echo date('Y年m月d日', strtotime($target_user['regtime'])); ?> | 
                        🆔 会员编号：#<?php echo str_pad($target_user['id'], 6, '0', STR_PAD_LEFT); ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="view-controls">
            <label>📋 查看模式：</label>
            <a href="?username=<?php echo urlencode($target_username); ?>&mode=all" 
               class="view-btn <?php echo $view_mode === 'all' ? 'active' : ''; ?>">
                📑 全部内容
            </a>
            <a href="?username=<?php echo urlencode($target_username); ?>&mode=general" 
               class="view-btn <?php echo $view_mode === 'general' ? 'active' : ''; ?>">
                📢 系统公告
            </a>
            <a href="?username=<?php echo urlencode($target_username); ?>&mode=personal" 
               class="view-btn <?php echo $view_mode === 'personal' ? 'active' : ''; ?>">
                🎯 专属内容
            </a>
        </div>
        
        <div class="content-container">
            <?php if ($view_mode === 'all' || $view_mode === 'general'): ?>
                <div class="content-panel">
                    <div class="content-header">
                        <h3>📢 系统公告</h3>
                        <div class="content-meta">
                            <?php if (isset($general_updated)): ?>
                                最后更新：<?php echo date('Y-m-d H:i:s', strtotime($general_updated)); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="content-body" id="generalContent">
                        <?php if ($general_content): ?>
                            <?php echo $general_content; ?>
                        <?php else: ?>
                            <div class="empty-content">
                                <div class="icon">📭</div>
                                <p>暂无系统公告</p>
                                <small>管理员尚未发布任何公告内容</small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if ($view_mode === 'all' || $view_mode === 'personal'): ?>
                <div class="content-panel">
                    <div class="content-header">
                        <h3>🎯 专属内容</h3>
                        <div class="content-meta">
                            <?php if ($personal_updated): ?>
                                最后更新：<?php echo date('Y-m-d H:i:s', strtotime($personal_updated)); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="content-body" id="personalContent">
                        <?php if ($personal_content): ?>
                            <?php echo $personal_content; ?>
                        <?php else: ?>
                            <div class="empty-content">
                                <div class="icon">🎁</div>
                                <p>暂无专属内容</p>
                                <small>管理员可为该用户分配专属内容</small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="action-buttons">
            <button onclick="window.print()" class="btn btn-info">🖨️ 打印内容</button>
            <button onclick="copyContent()" class="btn btn-success">📋 复制内容</button>
            <?php if ($_SESSION['ok'] == 1): ?>
                <a href="u.php?username=<?php echo urlencode($target_username); ?>" class="btn">✏️ 编辑内容</a>
            <?php endif; ?>
            <a href="e.php" class="btn btn-secondary">🏠 返回主页</a>
        </div>
    </div>
    
    <script>
        // 复制内容功能
        function copyContent() {
            let content = '';
            const generalContent = document.getElementById('generalContent');
            const personalContent = document.getElementById('personalContent');
            
            if (generalContent && !generalContent.querySelector('.empty-content')) {
                content += '=== 系统公告 ===\n';
                content += generalContent.innerText + '\n\n';
            }
            
            if (personalContent && !personalContent.querySelector('.empty-content')) {
                content += '=== 专属内容 ===\n';
                content += personalContent.innerText + '\n\n';
            }
            
            if (content.trim()) {
                content += '--- 来源：<?php echo SYSTEM_NAME; ?> ---\n';
                content += '生成时间：' + new Date().toLocaleString();
                
                navigator.clipboard.writeText(content).then(function() {
                    alert('✅ 内容已复制到剪贴板！');
                }).catch(function() {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = content;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('✅ 内容已复制到剪贴板！');
                });
            } else {
                alert('❌ 没有可复制的内容！');
            }
        }
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach((panel, index) => {
                panel.style.opacity = '0';
                panel.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    panel.style.transition = 'all 0.6s ease';
                    panel.style.opacity = '1';
                    panel.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
        
        // 内容区域滚动优化
        document.querySelectorAll('.content-body').forEach(element => {
            element.addEventListener('scroll', function() {
                if (this.scrollTop > 0) {
                    this.style.boxShadow = 'inset 0 10px 10px -10px rgba(0,0,0,0.1)';
                } else {
                    this.style.boxShadow = 'none';
                }
            });
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 'p':
                        e.preventDefault();
                        window.print();
                        break;
                    case 'c':
                        if (e.shiftKey) {
                            e.preventDefault();
                            copyContent();
                        }
                        break;
                }
            }
        });
    </script>
</body>
</html>
