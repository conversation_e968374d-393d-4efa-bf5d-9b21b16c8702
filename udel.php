<?php
// 会员管理系统 - 清空用户内容
// 深红色主题版本

require_once 'config.php';
check_permission(1); // 只允许管理员访问

$pdo = connectDB();

// 获取目标用户名
$target_username = $_GET['username'] ?? '';
if (empty($target_username)) {
    header('Location: user.php');
    exit();
}

// 验证用户是否存在
$user_stmt = safe_query($pdo, "SELECT * FROM tab WHERE username = ?", [$target_username]);
$target_user = $user_stmt->fetch();
if (!$target_user) {
    header('Location: user.php?error=用户不存在');
    exit();
}

$message = '';
$error = '';

// 处理删除请求
if ($_POST && isset($_POST['confirm_delete'])) {
    try {
        // 删除用户的个人内容
        $stmt = $pdo->prepare("DELETE FROM content WHERE username = ? AND type = 'personal'");
        $result = $stmt->execute([$target_username]);
        
        if ($result) {
            $affected_rows = $stmt->rowCount();
            $message = "成功清空用户 '$target_username' 的个人内容！共删除 $affected_rows 条记录。";
        } else {
            $error = '清空失败，请稍后重试！';
        }
    } catch (Exception $e) {
        $error = '清空失败：' . $e->getMessage();
        error_log("清空用户内容错误: " . $e->getMessage());
    }
}

// 获取用户的内容统计
$content_count = 0;
$content_stmt = safe_query($pdo, "SELECT COUNT(*) FROM content WHERE username = ? AND type = 'personal'", [$target_username]);
if ($content_stmt) {
    $content_count = $content_stmt->fetchColumn();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 清空用户内容</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            min-height: 100vh;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .container {
            max-width: 600px;
            width: 90%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }
        
        .warning-icon {
            font-size: 4em;
            color: #dc3545;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .title {
            color: #8B0000;
            font-size: 2.2em;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .user-info {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #8B0000;
        }
        
        .user-info h3 {
            color: #8B0000;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .user-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            text-align: left;
        }
        
        .user-detail {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .user-detail .label {
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }
        
        .user-detail .value {
            color: #666;
            font-size: 16px;
        }
        
        .warning-message {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            color: #856404;
        }
        
        .warning-message h4 {
            color: #dc3545;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .warning-message ul {
            text-align: left;
            margin-left: 20px;
        }
        
        .warning-message li {
            margin-bottom: 8px;
        }
        
        .content-stats {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            border-left: 4px solid #8B0000;
        }
        
        .content-stats h4 {
            color: #8B0000;
            margin-bottom: 10px;
        }
        
        .stats-number {
            font-size: 2em;
            font-weight: bold;
            color: #dc3545;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
        }
        
        .btn-danger:hover {
            box-shadow: 0 15px 30px rgba(220, 53, 69, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 15px 30px rgba(108, 117, 125, 0.4);
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .confirmation-form {
            margin-top: 20px;
        }
        
        .checkbox-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }
        
        .checkbox-container input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: #dc3545;
        }
        
        .checkbox-container label {
            color: #dc3545;
            font-weight: bold;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 25px;
                margin: 20px;
            }
            
            .user-details {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="warning-icon">⚠️</div>
        <h1 class="title">清空用户内容</h1>
        
        <div class="user-info">
            <h3>🎯 目标用户信息</h3>
            <div class="user-details">
                <div class="user-detail">
                    <span class="label">用户名</span>
                    <span class="value"><?php echo safe_output($target_user['username']); ?></span>
                </div>
                <div class="user-detail">
                    <span class="label">权限等级</span>
                    <span class="value">
                        <?php if ($target_user['ok'] == 1): ?>
                            👑 管理员
                        <?php else: ?>
                            🎯 普通用户
                        <?php endif; ?>
                    </span>
                </div>
                <div class="user-detail">
                    <span class="label">注册时间</span>
                    <span class="value"><?php echo date('Y-m-d H:i', strtotime($target_user['regtime'])); ?></span>
                </div>
                <div class="user-detail">
                    <span class="label">用户ID</span>
                    <span class="value">#<?php echo $target_user['id']; ?></span>
                </div>
            </div>
        </div>
        
        <div class="content-stats">
            <h4>📊 内容统计</h4>
            <div class="stats-number"><?php echo $content_count; ?></div>
            <p>条个人专属内容</p>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-error">
                ❌ <?php echo safe_output($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($message): ?>
            <div class="alert alert-success">
                ✅ <?php echo safe_output($message); ?>
            </div>
            <div class="button-group">
                <a href="user.php" class="btn btn-secondary">👥 返回用户列表</a>
                <a href="u.php?username=<?php echo urlencode($target_username); ?>" class="btn btn-secondary">📝 编辑内容</a>
            </div>
        <?php else: ?>
            <div class="warning-message">
                <h4>🚨 危险操作警告</h4>
                <ul>
                    <li>此操作将<strong>永久删除</strong>该用户的所有个人专属内容</li>
                    <li>删除后的内容<strong>无法恢复</strong></li>
                    <li>不会影响用户账户本身，只清空内容数据</li>
                    <li>建议在执行前先备份重要数据</li>
                </ul>
            </div>
            
            <?php if ($content_count > 0): ?>
                <form method="POST" action="" class="confirmation-form">
                    <div class="checkbox-container">
                        <input type="checkbox" id="confirm_checkbox" required>
                        <label for="confirm_checkbox">我确认要清空该用户的所有个人内容</label>
                    </div>
                    
                    <div class="button-group">
                        <button type="submit" name="confirm_delete" value="1" class="btn btn-danger" 
                                onclick="return confirm('最后确认：确定要清空用户 <?php echo safe_output($target_username); ?> 的所有个人内容吗？')">
                            🗑️ 确认清空
                        </button>
                        <a href="user.php" class="btn btn-secondary">❌ 取消操作</a>
                    </div>
                </form>
            <?php else: ?>
                <div class="alert alert-success">
                    ℹ️ 该用户暂无个人内容，无需清空。
                </div>
                <div class="button-group">
                    <a href="user.php" class="btn btn-secondary">👥 返回用户列表</a>
                    <a href="u.php?username=<?php echo urlencode($target_username); ?>" class="btn btn-secondary">📝 编辑内容</a>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    
    <script>
        // 表单提交前的最终确认
        document.querySelector('form')?.addEventListener('submit', function(e) {
            const checkbox = document.getElementById('confirm_checkbox');
            if (!checkbox.checked) {
                e.preventDefault();
                alert('请先勾选确认复选框！');
                return false;
            }
            
            const username = '<?php echo safe_output($target_username); ?>';
            const contentCount = <?php echo $content_count; ?>;
            
            if (!confirm(`最后确认：\n\n用户：${username}\n内容数量：${contentCount} 条\n\n确定要清空吗？此操作不可恢复！`)) {
                e.preventDefault();
                return false;
            }
        });
        
        // 复选框状态改变时更新按钮状态
        document.getElementById('confirm_checkbox')?.addEventListener('change', function() {
            const submitBtn = document.querySelector('button[name="confirm_delete"]');
            if (submitBtn) {
                if (this.checked) {
                    submitBtn.style.opacity = '1';
                    submitBtn.style.pointerEvents = 'auto';
                } else {
                    submitBtn.style.opacity = '0.5';
                    submitBtn.style.pointerEvents = 'none';
                }
            }
        });
        
        // 初始化按钮状态
        window.addEventListener('load', function() {
            const checkbox = document.getElementById('confirm_checkbox');
            const submitBtn = document.querySelector('button[name="confirm_delete"]');
            if (checkbox && submitBtn && !checkbox.checked) {
                submitBtn.style.opacity = '0.5';
                submitBtn.style.pointerEvents = 'none';
            }
        });
    </script>
</body>
</html>
