<?php
// 会员管理系统 - 安全退出
// 深红色主题版本

session_start();

// 记录退出日志
if (isset($_SESSION['username'])) {
    $logout_info = [
        'username' => $_SESSION['username'],
        'logout_time' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    error_log("用户退出: " . json_encode($logout_info, JSON_UNESCAPED_UNICODE));
}

// 销毁所有Session数据
$_SESSION = array();

// 删除Session Cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// 销毁Session
session_destroy();

// 清除可能的缓存
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

// 跳转到登录页面
header('Location: index.php?logout=1');
exit();
?>
