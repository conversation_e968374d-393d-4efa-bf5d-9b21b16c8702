<?php
// 会员管理系统 - 个人内容编辑
// 深红色主题版本

require_once 'config.php';
check_permission(1); // 只允许管理员访问

$pdo = connectDB();

// 获取目标用户名
$target_username = $_GET['username'] ?? '';
if (empty($target_username)) {
    header('Location: user.php');
    exit();
}

// 验证用户是否存在
$user_stmt = safe_query($pdo, "SELECT * FROM tab WHERE username = ?", [$target_username]);
$target_user = $user_stmt->fetch();
if (!$target_user) {
    header('Location: user.php?error=用户不存在');
    exit();
}

// 获取用户的个人内容
$content_stmt = safe_query($pdo, "SELECT content FROM content WHERE username = ? AND type = 'personal' ORDER BY updated_time DESC LIMIT 1", [$target_username]);
$current_content = '';
if ($content_row = $content_stmt->fetch()) {
    $current_content = $content_row['content'];
}

$message = '';
$error = '';

// 处理内容保存
if ($_POST && isset($_POST['content'])) {
    $new_content = $_POST['content'] ?? '';
    
    try {
        // 删除该用户的旧个人内容
        $pdo->prepare("DELETE FROM content WHERE username = ? AND type = 'personal'")->execute([$target_username]);
        
        // 如果有新内容，则插入
        if (!empty($new_content)) {
            $stmt = $pdo->prepare("INSERT INTO content (username, content, type) VALUES (?, ?, 'personal')");
            $stmt->execute([$target_username, $new_content]);
        }
        
        $message = "用户 '$target_username' 的个人内容已更新！";
        $current_content = $new_content;
    } catch (Exception $e) {
        $error = '保存失败：' . $e->getMessage();
        error_log("保存个人内容错误: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 编辑个人内容</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            min-height: 100vh;
            color: #333;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            border-bottom: 3px solid #8B0000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #8B0000;
            font-size: 2.2em;
            font-weight: 700;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-badge {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-links a {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }
        
        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .editor-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .panel-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #8B0000;
        }
        
        .panel-header h2 {
            color: #8B0000;
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .panel-header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .target-user-info {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #8B0000;
        }
        
        .target-user-info h3 {
            color: #8B0000;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .user-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .user-detail {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-detail .label {
            font-weight: bold;
            color: #333;
        }
        
        .user-detail .value {
            color: #666;
        }
        
        .editor-container {
            margin-bottom: 30px;
        }
        
        .editor-container label {
            display: block;
            margin-bottom: 15px;
            color: #333;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .ckeditor-textarea {
            width: 100%;
            min-height: 400px;
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 20px;
            font-size: 14px;
            resize: vertical;
            transition: all 0.3s ease;
        }
        
        .ckeditor-textarea:focus {
            border-color: #8B0000;
            box-shadow: 0 0 15px rgba(139, 0, 0, 0.2);
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(139, 0, 0, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .content-preview {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            border-left: 4px solid #8B0000;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .content-preview h4 {
            color: #8B0000;
            margin-bottom: 15px;
        }
        
        .empty-content {
            color: #999;
            font-style: italic;
            text-align: center;
            padding: 40px;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .container {
                margin: 20px auto;
            }
            
            .editor-panel {
                padding: 25px;
            }
            
            .user-details {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>📝 编辑个人内容</h1>
            <div class="user-info">
                <div class="user-badge">👑 管理员: <?php echo safe_output($_SESSION['username']); ?></div>
                <div class="nav-links">
                    <a href="user.php">👥 返回列表</a>
                    <a href="c.php">🏛️ 控制台</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="editor-panel">
            <div class="panel-header">
                <h2>✏️ 个人内容编辑器</h2>
                <p>为指定用户创建或编辑专属内容</p>
            </div>
            
            <div class="target-user-info">
                <h3>🎯 目标用户信息</h3>
                <div class="user-details">
                    <div class="user-detail">
                        <span class="label">用户名:</span>
                        <span class="value"><?php echo safe_output($target_user['username']); ?></span>
                    </div>
                    <div class="user-detail">
                        <span class="label">权限等级:</span>
                        <span class="value">
                            <?php if ($target_user['ok'] == 1): ?>
                                👑 管理员
                            <?php else: ?>
                                🎯 普通用户
                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="user-detail">
                        <span class="label">注册时间:</span>
                        <span class="value"><?php echo date('Y-m-d H:i', strtotime($target_user['regtime'])); ?></span>
                    </div>
                    <div class="user-detail">
                        <span class="label">用户ID:</span>
                        <span class="value">#<?php echo $target_user['id']; ?></span>
                    </div>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    ❌ <?php echo safe_output($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    ✅ <?php echo safe_output($message); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="" id="contentForm">
                <div class="editor-container">
                    <label for="editor">📄 个人专属内容</label>
                    <textarea name="content" id="editor" class="ckeditor-textarea" 
                              placeholder="在此为用户 <?php echo safe_output($target_username); ?> 编辑专属内容..."><?php echo safe_output($current_content); ?></textarea>
                </div>
                
                <div class="button-group">
                    <button type="submit" class="btn">💾 保存内容</button>
                    <button type="button" class="btn btn-secondary" onclick="clearEditor()">🗑️ 清空编辑器</button>
                    <a href="check.php?username=<?php echo urlencode($target_username); ?>" class="btn btn-info">👁️ 预览内容</a>
                    <a href="udel.php?username=<?php echo urlencode($target_username); ?>" class="btn btn-danger"
                       onclick="return confirm('确定要清空 <?php echo safe_output($target_username); ?> 的所有个人内容吗？')">
                        🗂️ 清空内容
                    </a>
                </div>
            </form>
            
            <?php if ($current_content): ?>
                <div class="content-preview">
                    <h4>📋 当前内容预览</h4>
                    <?php echo $current_content; ?>
                </div>
            <?php else: ?>
                <div class="content-preview">
                    <div class="empty-content">
                        📭 该用户暂无个人专属内容
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="ckeditor/ckeditor.js"></script>
    <script>
        // 初始化CKEditor
        CKEDITOR.replace('editor', {
            height: 400,
            language: 'zh-cn',
            toolbar: [
                ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript'],
                ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', 'Blockquote'],
                ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
                ['Link', 'Unlink', 'Anchor'],
                ['Image', 'Table', 'HorizontalRule', 'SpecialChar'],
                ['TextColor', 'BGColor'],
                ['Styles', 'Format', 'Font', 'FontSize'],
                ['Maximize', 'ShowBlocks', 'Source']
            ],
            removePlugins: 'elementspath',
            resize_enabled: true,
            filebrowserUploadUrl: 'upload.php' // 如果需要图片上传功能
        });
        
        // 清空编辑器
        function clearEditor() {
            if (confirm('确定要清空编辑器内容吗？此操作不可恢复！')) {
                CKEDITOR.instances.editor.setData('');
            }
        }
        
        // 自动保存草稿
        let autoSaveTimer;
        function autoSave() {
            const content = CKEDITOR.instances.editor.getData();
            if (content.trim()) {
                localStorage.setItem('draft_<?php echo $target_username; ?>', content);
                console.log('草稿已自动保存');
            }
        }
        
        // 每30秒自动保存一次
        CKEDITOR.instances.editor.on('instanceReady', function() {
            autoSaveTimer = setInterval(autoSave, 30000);
        });
        
        // 页面加载时恢复草稿
        window.addEventListener('load', function() {
            const draft = localStorage.getItem('draft_<?php echo $target_username; ?>');
            if (draft && !CKEDITOR.instances.editor.getData().trim()) {
                if (confirm('发现该用户的未保存草稿，是否恢复？')) {
                    CKEDITOR.instances.editor.setData(draft);
                }
            }
        });
        
        // 表单提交前确认
        document.getElementById('contentForm').addEventListener('submit', function(e) {
            const content = CKEDITOR.instances.editor.getData();
            if (!content.trim()) {
                if (!confirm('内容为空，确定要清空该用户的个人内容吗？')) {
                    e.preventDefault();
                    return false;
                }
            }
            
            // 保存成功后清除草稿
            localStorage.removeItem('draft_<?php echo $target_username; ?>');
        });
        
        // 页面离开前提醒保存
        window.addEventListener('beforeunload', function(e) {
            const content = CKEDITOR.instances.editor.getData();
            const originalContent = '<?php echo addslashes($current_content); ?>';
            
            if (content !== originalContent && content.trim()) {
                e.preventDefault();
                e.returnValue = '您有未保存的更改，确定要离开吗？';
                return e.returnValue;
            }
        });
        
        // 清理定时器
        window.addEventListener('unload', function() {
            if (autoSaveTimer) {
                clearInterval(autoSaveTimer);
            }
        });
    </script>
</body>
</html>
