<?php
// 会员管理系统安装程序
// 深红色主题版本

// 检查是否已经安装
if (file_exists('../config.php') && file_exists('../install.lock')) {
    die('系统已经安装！如需重新安装，请删除 install.lock 文件。');
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// 处理安装步骤
if ($_POST) {
    switch ($step) {
        case 1:
            // 数据库连接测试
            $host = $_POST['db_host'] ?? 'localhost';
            $username = $_POST['db_username'] ?? 'root';
            $password = $_POST['db_password'] ?? '';
            $dbname = $_POST['db_name'] ?? 'member_system';
            
            try {
                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // 创建数据库
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `$dbname`");
                
                // 创建用户表
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS `tab` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `username` varchar(50) NOT NULL,
                        `password` varchar(255) NOT NULL,
                        `ok` tinyint(1) NOT NULL DEFAULT 2 COMMENT '1=管理员, 2=普通用户',
                        `regtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `username` (`username`),
                        KEY `ok` (`ok`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ");
                
                // 创建内容表
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS `content` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `username` varchar(50) NOT NULL,
                        `content` longtext NOT NULL,
                        `type` varchar(20) NOT NULL DEFAULT 'general' COMMENT 'general=通用内容, personal=个人内容',
                        `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        KEY `username` (`username`),
                        KEY `type` (`type`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ");
                
                // 插入默认管理员账户
                $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
                $pdo->prepare("INSERT IGNORE INTO `tab` (username, password, ok) VALUES (?, ?, 1)")
                    ->execute(['admin', $admin_password]);
                
                // 更新配置文件
                $config_content = "<?php
// 数据库配置文件
// 会员管理系统 - 深红色主题版本

// 数据库连接配置
\$db_host = '$host';
\$db_username = '$username';
\$db_password = '$password';
\$db_name = '$dbname';

// 数据库连接函数
function connectDB() {
    global \$db_host, \$db_username, \$db_password, \$db_name;
    
    try {
        \$pdo = new PDO(\"mysql:host=\$db_host;dbname=\$db_name;charset=utf8mb4\", 
                       \$db_username, \$db_password);
        \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return \$pdo;
    } catch(PDOException \$e) {
        die(\"数据库连接失败: \" . \$e->getMessage());
    }
}

// 兼容旧版MySQL函数的连接
function mysql_connect_compat() {
    global \$db_host, \$db_username, \$db_password, \$db_name;
    
    try {
        \$mysqli = new mysqli(\$db_host, \$db_username, \$db_password, \$db_name);
        if (\$mysqli->connect_error) {
            die('连接失败: ' . \$mysqli->connect_error);
        }
        \$mysqli->set_charset(\"utf8mb4\");
        return \$mysqli;
    } catch(Exception \$e) {
        die(\"数据库连接失败: \" . \$e->getMessage());
    }
}

// 安全函数：防止SQL注入
function safe_query(\$pdo, \$sql, \$params = []) {
    try {
        \$stmt = \$pdo->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt;
    } catch(PDOException \$e) {
        error_log(\"SQL错误: \" . \$e->getMessage());
        return false;
    }
}

// HTML转义函数
function safe_output(\$string) {
    return htmlspecialchars(\$string, ENT_QUOTES, 'UTF-8');
}

// 检查用户权限
function check_permission(\$required_level = 1) {
    session_start();
    if (!isset(\$_SESSION['username']) || !isset(\$_SESSION['ok'])) {
        header('Location: index.php');
        exit();
    }
    
    if (\$_SESSION['ok'] < \$required_level) {
        die('权限不足！');
    }
    
    return true;
}

// 系统配置
define('SYSTEM_NAME', '会员管理系统');
define('VERSION', '2.0');
define('THEME_COLOR', '#8B0000'); // 深红色主题

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');
?>";
                
                file_put_contents('../config.php', $config_content);
                
                // 创建安装锁定文件
                file_put_contents('../install.lock', date('Y-m-d H:i:s'));
                
                $success = '安装成功！默认管理员账户：admin / admin123';
                $step = 2;
                
            } catch (Exception $e) {
                $error = '安装失败：' . $e->getMessage();
            }
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员管理系统 - 安装程序</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .install-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-header h1 {
            color: #8B0000;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .install-header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #8B0000;
            box-shadow: 0 0 10px rgba(139, 0, 0, 0.2);
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 0, 0, 0.3);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .alert-error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .alert-success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            color: white;
        }
        
        .step.active {
            background: #8B0000;
        }
        
        .step.inactive {
            background: #ccc;
        }
        
        .success-message {
            text-align: center;
            padding: 40px 20px;
        }
        
        .success-message h2 {
            color: #2e7d32;
            margin-bottom: 20px;
        }
        
        .success-message .login-link {
            display: inline-block;
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 10px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .success-message .login-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>🔧 系统安装</h1>
            <p>会员管理系统 - 深红色主题版</p>
        </div>
        
        <div class="step-indicator">
            <div class="step <?php echo $step == 1 ? 'active' : 'inactive'; ?>">1</div>
            <div class="step <?php echo $step == 2 ? 'active' : 'inactive'; ?>">2</div>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-error">❌ <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">✅ <?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <?php if ($step == 1): ?>
            <form method="POST">
                <div class="form-group">
                    <label for="db_host">数据库主机</label>
                    <input type="text" id="db_host" name="db_host" value="localhost" required>
                </div>
                
                <div class="form-group">
                    <label for="db_username">数据库用户名</label>
                    <input type="text" id="db_username" name="db_username" value="root" required>
                </div>
                
                <div class="form-group">
                    <label for="db_password">数据库密码</label>
                    <input type="password" id="db_password" name="db_password" value="111111" required>
                </div>
                
                <div class="form-group">
                    <label for="db_name">数据库名称</label>
                    <input type="text" id="db_name" name="db_name" value="member_system" required>
                </div>
                
                <button type="submit" class="btn">开始安装</button>
            </form>
        <?php elseif ($step == 2): ?>
            <div class="success-message">
                <h2>🎉 安装完成！</h2>
                <p>系统已成功安装并配置完成。</p>
                <p><strong>默认管理员账户：</strong></p>
                <p>用户名：<code>admin</code></p>
                <p>密码：<code>admin123</code></p>
                <p style="color: #ff6b6b; margin-top: 20px;">
                    <strong>⚠️ 安全提示：</strong><br>
                    请立即登录系统并修改默认密码！<br>
                    建议删除 install 目录以提高安全性。
                </p>
                <a href="../index.php" class="login-link">立即登录系统</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
